server {
    server_name admin.basmatinho.it;
    root /var/www/html/admin_panel;

    index index.php;
    charset utf-8;

    location = /favicon.ico { access_log off; log_not_found off; }
    location = /robots.txt  { access_log off; log_not_found off; }

    # Handle /public requests by serving from the public directory
    location /public {
        alias /var/www/html/admin_panel/public;
        try_files $uri $uri/ =404;
        
        location ~ \.php$ {
            fastcgi_pass unix:/var/run/php/php8.3-fpm.sock;
            fastcgi_param SCRIPT_FILENAME $request_filename;
            include fastcgi_params;
            fastcgi_index index.php;
        }
    }

    location / {
        try_files $uri $uri/ /public/index.php?$query_string;
    }

    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.3-fpm.sock;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
        fastcgi_index index.php;
    }

    location ~ /\. {
        deny all;
    }

    location ~ ^/(\.env|\.git|composer\.(json|lock)|package\.json|gulpfile\.js|webpack\.mix\.js|artisan) {
        deny all;
    }

    error_log /var/log/nginx/admin.basmatinho.it_error.log;
    access_log /var/log/nginx/admin.basmatinho.it_access.log;

    listen [::]:443 ssl ipv6only=on; # managed by Certbot
    listen 443 ssl; # managed by Certbot
    ssl_certificate /etc/letsencrypt/live/admin.basmatinho.it/fullchain.pem; # managed by Certbot
    ssl_certificate_key /etc/letsencrypt/live/admin.basmatinho.it/privkey.pem; # managed by Certbot
    include /etc/letsencrypt/options-ssl-nginx.conf; # managed by Certbot
    ssl_dhparam /etc/letsencrypt/ssl-dhparams.pem; # managed by Certbot

}
server {
    if ($host = admin.basmatinho.it) {
        return 301 https://$host$request_uri;
    } # managed by Certbot


    listen 80;
    listen [::]:80;
    server_name admin.basmatinho.it;
    return 404; # managed by Certbot


}
