server {
    listen 80;
    listen [::]:80;
    server_name admin.basmatinho.it;
    root /var/www/html/admin_panel/public;

    # Index files
    index index.php;

    # Character set
    charset utf-8;

    # Disable access logs for common files
    location = /favicon.ico { access_log off; log_not_found off; }
    location = /robots.txt  { access_log off; log_not_found off; }

    # Laravel application
    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    # PHP-FPM configuration
    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.3-fpm.sock;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
        fastcgi_index index.php;
    }

    # Deny access to hidden files
    location ~ /\. {
        deny all;
    }

    # Deny access to sensitive Laravel files
    location ~ ^/(\.env|\.git|composer\.(json|lock)|package\.json|gulpfile\.js|webpack\.mix\.js|artisan) {
        deny all;
    }

    # Error and access logs
    error_log /var/log/nginx/admin.basmatinho.it_error.log;
    access_log /var/log/nginx/admin.basmatinho.it_access.log;
}


