server {
    listen 80;
    listen [::]:80;
    server_name admin.basmatinho.it;
    root /var/www/html/admin_panel/public;

    index index.php;
    charset utf-8;

    location = /favicon.ico { access_log off; log_not_found off; }
    location = /robots.txt  { access_log off; log_not_found off; }

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.3-fpm.sock;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
        fastcgi_index index.php;
    }

    location ~ /\. {
        deny all;
    }

    location ~ ^/(\.env|\.git|composer\.(json|lock)|package\.json|gulpfile\.js|webpack\.mix\.js|artisan) {
        deny all;
    }

    error_log /var/log/nginx/admin.basmatinho.it_error.log;
    access_log /var/log/nginx/admin.basmatinho.it_access.log;
}
