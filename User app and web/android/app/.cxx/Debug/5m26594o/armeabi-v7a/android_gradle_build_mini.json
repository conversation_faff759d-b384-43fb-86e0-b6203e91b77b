{"buildFiles": ["/Users/<USER>/Documents/FlutterSdk/flutter/packages/flutter_tools/gradle/src/main/groovy/CMakeLists.txt"], "cleanCommandsComponents": [["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/Documents/6amtech/StackFood/StackFood-User-App/android/app/.cxx/Debug/5m26594o/armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/Documents/6amtech/StackFood/StackFood-User-App/android/app/.cxx/Debug/5m26594o/armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}