{"buildFiles": ["/Users/<USER>/packages/flutter_tools/gradle/src/main/groovy/CMakeLists.txt"], "cleanCommandsComponents": [["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/Pictures/StackFood-User-App/android/app/.cxx/Debug/2y383t42/armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/Pictures/StackFood-User-App/android/app/.cxx/Debug/2y383t42/armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}