{"buildFiles": ["/Users/<USER>/packages/flutter_tools/gradle/src/main/groovy/CMakeLists.txt"], "cleanCommandsComponents": [["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/Pictures/StackFood-User-App/android/app/.cxx/Debug/2y383t42/x86", "clean"]], "buildTargetsCommandComponents": ["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/Pictures/StackFood-User-App/android/app/.cxx/Debug/2y383t42/x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}